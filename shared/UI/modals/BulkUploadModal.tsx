// shared/UI/modals/BulkUploadModal.tsx
'use client';

import React, { useState, useRef } from 'react';
import BaseModal from './BaseModal';
import { PrimaryButton } from '../buttons';
import { useToast } from '../notifications';
import { useBulkUpload } from '@/shared/hooks/business/useBulkUpload';

interface BulkUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
}

/**
 * Bulk Upload Modal Component
 * 
 * Modal component for uploading users and RFID details via CSV
 * Uses BaseModal for consistent styling and behavior
 * 
 * Features:
 * - File input for CSV upload
 * - Sample CSV download functionality
 * - Upload CSV functionality with progress indication
 * - Error handling and success notifications
 * - Dark theme styling consistent with other modals
 */
const BulkUploadModal: React.FC<BulkUploadModalProps> = ({
  isOpen,
  onClose
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { showError } = useToast();
  const { downloadSampleCsv, uploadCsv, isDownloadingSample, isUploading } = useBulkUpload();

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.name.toLowerCase().endsWith('.csv')) {
        showError('Invalid File Type', 'Please select a CSV file.');
        return;
      }
      setSelectedFile(file);
    }
  };

  // Handle sample CSV download
  const handleSampleDownload = async () => {
    await downloadSampleCsv();
  };

  // Handle CSV upload
  const handleUpload = async () => {
    if (!selectedFile) {
      showError('No File Selected', 'Please select a CSV file to upload.');
      return;
    }

    try {
      await uploadCsv(selectedFile);
      // Clear file selection and close modal on success
      setSelectedFile(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      onClose();
    } catch (error) {
      //eslint-disable-next-line no-console
      console.error(error, 'error');
      // Error handling is done in the hook
    }
  };

  // SVG icon for the modal header (user with plus symbol in orange theme)
  const headerIcon = (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M12 12C14.7614 12 17 9.76142 17 7C17 4.23858 14.7614 2 12 2C9.23858 2 7 4.23858 7 7C7 9.76142 9.23858 12 12 12Z"
        fill="#F59E0B"
      />
      <path
        d="M12 14C7.58172 14 4 17.5817 4 22H20C20 17.5817 16.4183 14 12 14Z"
        fill="#F59E0B"
      />
      <path
        d="M19 8H21V10H19V12H17V10H15V8H17V6H19V8Z"
        fill="#F59E0B"
      />
    </svg>
  );

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title="Upload users and RFID Details"
      headerIcon={headerIcon}
      size="md"
      className="bulk-upload-modal"
    >
      <div className="space-y-6">
        {/* File Input Section */}
        <div className="space-y-3">
          <label className="block text-sm font-medium text-text-primary">
            Select CSV File
          </label>
          <div className="relative">
            <input
              ref={fileInputRef}
              type="file"
              accept=".csv"
              onChange={handleFileSelect}
              className="block w-full text-sm text-text-muted
                file:mr-4 file:py-2 file:px-4
                file:rounded-md file:border-0
                file:text-sm file:font-medium
                file:bg-primary file:text-white
                hover:file:bg-primary/90
                file:cursor-pointer cursor-pointer"
            />
          </div>
          {selectedFile && (
            <p className="text-sm text-text-muted">
              Selected: {selectedFile.name} ({(selectedFile.size / 1024).toFixed(2)} KB)
            </p>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col space-y-3">
          {/* Sample CSV Button */}
          <PrimaryButton
            size="sm"
            onClick={handleSampleDownload}
            disabled={isDownloadingSample}
            icon={{
              type: 'FONT_ICON',
              iconClass: 'ri-download-line',
              library: 'remix'
            }}
            iconPosition="left"
            className="w-[140px]"
          >
            {isDownloadingSample ? 'Downloading...' : 'Sample CSV'}
          </PrimaryButton>

          {/* Upload CSV Button */}
          <PrimaryButton
            size="lg"
            onClick={handleUpload}
            disabled={!selectedFile || isUploading}
            icon={{
              type: 'FONT_ICON',
              iconClass: isUploading ? 'ri-loader-4-line animate-spin' : 'ri-upload-line',
              library: 'remix'
            }}
            iconPosition="left"
            className="w-full"
          >
            {isUploading ? 'Uploading...' : 'Upload CSV'}
          </PrimaryButton>
        </div>

        {/* Help Text */}
        <div className="text-sm text-text-muted space-y-2">
          <p>
            <strong>Instructions:</strong>
          </p>
          <ul className="list-disc list-inside space-y-1 ml-2">
            <li>Download the sample CSV to see the required format</li>
            <li>Fill in your user and RFID data following the sample format</li>
            <li>Upload your completed CSV file</li>
            <li>The system will process and create users with RFID details</li>
          </ul>
        </div>
      </div>
    </BaseModal>
  );
};

export default BulkUploadModal;
